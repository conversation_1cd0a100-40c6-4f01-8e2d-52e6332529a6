<template>
  <div class="container">
    <header>
      <h1>Quiz App</h1>
      <input type="text" placeholder="Search...">
    </header>
    <div class="options-container">
      <div class="card">
        <img src="./assets/DSC_0425_1.JPG" alt="Math Quiz">
        <div class="card-text">
          <h2>Math</h2>
          <p>15 questions</p>
        </div>
      </div>
    </div>
  </div>
</template>


<style scoped>
  .container {
    width: 1000px;
    height: 1000px;
    margin: 0 auto;
    color: var(--color-text);
  }

  header {
    margin-bottom: 10px;
    margin-top: 30px;
    display: flex;
    align-items: center;
  }

  header h1 {
    font-weight: bold;
    margin-right: 30px;
    color: var(--color-heading);
  }

  header input {
    border: none;
    background-color: var(--color-background-mute);
    color: var(--color-text);
    padding: 10px;
    border-radius: 5px;
    font-size: 1.5rem;
    width: 400px;
  }

  header input::placeholder {
    color: var(--color-text);
    opacity: 0.6;
  }

  .options-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
  }

  .card {
    width: 300px;
    height: 300px;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    cursor: pointer;
    display: flex;
    flex-direction: column;
  }

  .card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    object-position: center;
  }

  .card-text {
    padding: 15px;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .card-text h2 {
    margin: 0 0 5px 0;
    color: var(--color-heading);
  }

  .card-text p {
    margin: 0;
    color: var(--color-text);
    opacity: 0.7;
  }


</style>